import React, { useEffect, useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { DateTime } from 'luxon';
import { InputText } from 'primereact/inputtext';
import { Calendar } from 'primereact/calendar';
import { MultiSelect } from 'primereact/multiselect';
import { Dialog } from 'primereact/dialog';
import APIServices from '../../../../service/APIService';
import { API } from '../../../../constants/api_url';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import moment from 'moment';
import DealerSubmissionView from './DealerSubmissionView';

const DealersSelfAssessmentTable = ({ data, dealerList, assessorList, globalFilter }) => {
    const [databk, setDatabk] = useState([]);
    const [datas, setDatas] = useState([]);
    const [search, setSearch] = useState('');
    const [dateFilter, setDateFilter] = useState({ start: null, end: null });
    const [dealerSelfSubmissions, setDealerSelfSubmissions] = useState([]);
    const [submissionDialog, setSubmissionDialog] = useState(false);
    const [submissionData, setSubmissionData] = useState(null);
    const [dealerInfo, setDealerInfo] = useState(null);

    // Define applyFilters function before using it in useEffect
    const applyFilters = (dataToFilter, searchValue = search) => {
        // Apply search filter
        let filteredData = dataToFilter;
        if (searchValue) {
            filteredData = filteredData.filter(x =>
                x?.vendor?.dealerName?.trim().toLowerCase().includes(searchValue?.trim().toLowerCase()) ||
                x?.vendor?.code?.trim().toLowerCase().includes(searchValue?.trim().toLowerCase())
            );
        }

        // Apply date range filter
        if (dateFilter.start && dateFilter.end) {
            filteredData = filteredData.filter(rowData => {
                // Get the latest self-assessment date for this dealer
                const dealerSubmissions = dealerSelfSubmissions.filter(sub => sub.dealerId === rowData.dealerId);
                if (!dealerSubmissions.length) return false;

                const latestSubmission = dealerSubmissions.sort((a, b) => {
                    const dateA = moment(a.reporting_period?.[0], 'MM-YYYY');
                    const dateB = moment(b.reporting_period?.[0], 'MM-YYYY');
                    return dateB - dateA;
                })[0];

                if (!latestSubmission?.reporting_period?.[0]) return false;

                const submissionDate = moment(latestSubmission.reporting_period[0], 'MM-YYYY').toDate();
                const startDate = moment(dateFilter.start).startOf('day').toDate();
                const endDate = moment(dateFilter.end).endOf('day').toDate();

                return submissionDate >= startDate && submissionDate <= endDate;
            });
        }

        // Add tableIndex property for sorting
        const indexedData = filteredData.map((item, index) => ({
            ...item,
            tableIndex: index + 1
        }));

        // Add grade property based on self-assessment score
        setDatas(indexedData.map(x => {
            const selfAssessmentScore = getSelfAssessmentScore(x);
            return {
                ...x,
                selfAssessmentGrade: getRatingName(selfAssessmentScore)
            };
        }));
    };

    useEffect(() => {

        
        setDatabk(data);
        applyFilters(data);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [data]);

    useEffect(() => {
        applyFilters(databk);
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dateFilter]);

    useEffect(() => {
        const fetchSubmissions = async () => {
            try {
                const res = await APIServices.get(API.DealerSelfSubmission);
                setDealerSelfSubmissions(res?.data || []);
            } catch (error) {
                console.error('Error fetching DealerSelfSubmission:', error);
            }
        };

        fetchSubmissions();
    }, []);

    const dealerType = [{ name: 'Authorized Main Dealer', value: 1 }, { name: 'Authorized Dealer', value: 2 }, { name: 'Authorized Parts Stockist (APS)', value: 3 }, { name: 'Area Office', value: 4 }]
       const zonalOfficeList = [{ name: "Central", value: 1 }, { name: "East", value: 2 }, { name: "North", value: 3 }, { name: "South", value: 9 }, { name: "South1", value: 4 }, { name: "South2", value: 5 }, { name: "West", value: 8 }, { name: "West1", value: 6 }, { name: "West2", value: 7 }, { name: "TN", value: 10 }, { name: "North1", value: 11 }, { name: "North2", value: 12 }]
    const searchFn = (e) => {
        let val = e.target.value;
        setSearch(val);
        applyFilters(databk, val);
    };

    const calibrationIdBodyTemplate = (rowData) => {
        return (
            <span>
                {'MSI-' + (rowData?.vendor?.code || 'NA') + '-' + DateTime.fromISO(rowData.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}
            </span>
        );
    };

    const nameTemplate = (rowData) => {
        return <div>{rowData?.vendor?.dealerName || 'NA'}</div>;
    };

    const locationTemplate = (rowData) => {
        return <div>{rowData?.vendor?.dealerLocation || 'NA'}</div>;
    };

    const zoneTemplate = (rowData) => {
        return <div>{zonalOfficeList.find(x => x.value === rowData?.vendor?.dealerZone)?.name || 'NA'}</div>;
    };

    const categoryTemplate = (rowData) => {
        return <div>{dealerType.find(x => x.value === rowData?.vendor?.dealerCategory)?.name || 'NA'}</div>;
    };

    const selfAssessmentMonthTemplate = (rowData) => {
        if (!Array.isArray(dealerSelfSubmissions)) return 'NA';

        const matched = dealerSelfSubmissions
            .filter(sub => sub.dealerId === rowData.dealerId)
            .sort((a, b) => {
                const dateA = moment(a.reporting_period?.[0], 'MM-YYYY');
                const dateB = moment(b.reporting_period?.[0], 'MM-YYYY');
                return dateB - dateA;
            });

        if (matched.length === 0 || !matched[0]?.reporting_period?.[0]) return 'NA';

        return moment(matched[0].reporting_period[0], 'MM-YYYY').format('MMMM YYYY');
    };

    const selfAssessmentScoreTemplate = (rowData) => {
        return getSelfAssessmentScore(rowData);
    };

    const getSelfAssessmentScore = (rowData) => {
        const matched = dealerSelfSubmissions
            .filter(sub => sub.dealerId === rowData.dealerId)
            .sort((a, b) => {
                const dateA = moment(a.reporting_period?.[0], 'MM-YYYY');
                const dateB = moment(b.reporting_period?.[0], 'MM-YYYY');
                return dateB - dateA;
            });

        if (matched.length > 0) {
            try {
                const scoreObj = JSON.parse(matched[0].score || '{}');
                return scoreObj.overallScore ?? 'NA';
            } catch (e) {
                return 'NA';
            }
        }

        return 'NA';
    };

    const selfAssessmentSubmitDateTemplate = (rowData) => {
        const matched = dealerSelfSubmissions
            .filter(sub => sub.dealerId === rowData.dealerId)
            .sort((a, b) => {
                const dateA = moment(a.reporting_period?.[0], 'MM-YYYY');
                const dateB = moment(b.reporting_period?.[0], 'MM-YYYY');
                return dateB - dateA;
            });

        if (matched.length > 0 && matched[0].created_on) {
            rowData.latestSubmission =  DateTime.fromISO(matched[0].created_on, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy');
            return DateTime.fromISO(matched[0].created_on, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy');
        }
        rowData.latestSubmission = ''
        return 'NA';
    };
    const getLastSubmissionDate = (rowData) => {
        const matched = dealerSelfSubmissions
            .filter(sub => sub.dealerId === rowData.dealerId)
            .sort((a, b) => {
                const dateA = moment(a.reporting_period?.[0], 'MM-YYYY');
                const dateB = moment(b.reporting_period?.[0], 'MM-YYYY');
                return dateB - dateA;
            });

        if (matched.length > 0 && matched[0].created_on) {
          return DateTime.fromISO(matched[0].created_on, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy');
        }

        return '';
    };
    const getRatingName = (score) => {
        if (!score || score === '-' || score === 'NA') return 'NA';
        score = parseFloat(score);
        if (score >= 85) return 'Platinum';
        if (score > 70) return 'Gold';
        if (score > 55) return 'Silver';
        return 'Not Met';
    };

    const ratingTemplate = (rowData) => {
        const score = getSelfAssessmentScore(rowData);
        if (score === 'NA') return 'NA';

        const scoreValue = parseFloat(score);
        return (
            <div style={{ width: scoreValue > 55 ? 50 : 80 }}>
                {scoreValue >= 85 ?
                    <img width={'100%'} alt="Platinum Rating" src={require('../../../../assets/images/report/valuechain/platinum_rating.png').default} /> :
                    scoreValue > 70 ?
                        <img width={'100%'} alt="Gold Rating" src={require('../../../../assets/images/report/valuechain/gold_rating.png').default} /> :
                        scoreValue > 55 ?
                            <img width={'100%'} alt="Silver Rating" src={require('../../../../assets/images/report/valuechain/silver_rating.png').default} /> :
                            scoreValue > 40 ?
                                "Bronze" :
                                "Needs Improvement"}
            </div>
        );
    };

    const sortSelfAssessmentScore = (e) => {
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const scoreA = getSelfAssessmentScore(a);
                const scoreB = getSelfAssessmentScore(b);

                if (scoreA === 'NA' && scoreB === 'NA') return 0;
                if (scoreA === 'NA') return 1; // 'NA' values at the end for ascending
                if (scoreB === 'NA') return -1;

                return parseFloat(scoreA) - parseFloat(scoreB);
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const scoreA = getSelfAssessmentScore(a);
                const scoreB = getSelfAssessmentScore(b);

                if (scoreA === 'NA' && scoreB === 'NA') return 0;
                if (scoreA === 'NA') return 1; // 'NA' values at the end for descending too
                if (scoreB === 'NA') return -1;

                return parseFloat(scoreB) - parseFloat(scoreA);
            });
        }
    };

    const sortSelfAssessmentMonth = (e) => {
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const monthA = selfAssessmentMonthTemplate(a);
                const monthB = selfAssessmentMonthTemplate(b);

                if (monthA === 'NA' && monthB === 'NA') return 0;
                if (monthA === 'NA') return 1;
                if (monthB === 'NA') return -1;

                const dateA = moment(monthA, 'MMMM YYYY');
                const dateB = moment(monthB, 'MMMM YYYY');
                return dateA - dateB;
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const monthA = selfAssessmentMonthTemplate(a);
                const monthB = selfAssessmentMonthTemplate(b);

                if (monthA === 'NA' && monthB === 'NA') return 0;
                if (monthA === 'NA') return 1;
                if (monthB === 'NA') return -1;

                const dateA = moment(monthA, 'MMMM YYYY');
                const dateB = moment(monthB, 'MMMM YYYY');
                return dateB - dateA;
            });
        }
    };
    const sortSelfAssessmentSubmitDate = (e) => {
        console.log(e.data)
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const monthA = a.latestSubmission
                const monthB = b.latestSubmission

                if (!monthA && !monthB ) return 0;
                if (!monthA ) return 1;
                if (!monthB ) return -1;

                const dateA = DateTime.fromFormat(monthA, 'dd-MM-yyyy')
                const dateB = DateTime.fromFormat(monthB, 'dd-MM-yyyy')
                console.log(dateA.isValid, dateB.isValid);
                if (dateA < dateB) return -1;
                if (dateA > dateB) return 1;
                return 0;
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const monthA = a.latestSubmission
                const monthB = b.latestSubmission

                if (!monthA && !monthB ) return 0;
                if (!monthA ) return 1;
                if (!monthB ) return -1;


                const dateA = DateTime.fromFormat(monthA, 'dd-MM-yyyy')
                const dateB = DateTime.fromFormat(monthB, 'dd-MM-yyyy')
                console.log(monthA,monthB);
                if (dateA > dateB) return -1;
                if (dateA < dateB) return 1;
                return 0;
            });
        }
    }
    const sortIndexColumn = (e) => {
        const { data, order } = e;

        // Create a new array with the current data and add an index property
        const indexedData = data.map((item, index) => ({
            ...item,
            tableIndex: index + 1
        }));

        // Sort based on the index
        if (order === 1) { // ascending
            return indexedData.sort((a, b) => a.tableIndex - b.tableIndex);
        } else { // descending
            return indexedData.sort((a, b) => b.tableIndex - a.tableIndex);
        }
    };

    // Group dealers by dealerId and keep only the latest entry for each dealer
    const groupDealersByIdAndGetLatest = (dealers) => {
       
        const dealerMap = new Map();

        dealers.forEach(dealer => {
            const dealerId = dealer.dealerId;

            // Skip dealers without valid self-assessment scores
            const selfAssessmentScore = getSelfAssessmentScore(dealer);
            if (selfAssessmentScore === 'NA' || selfAssessmentScore === '-') {
                return;
            }

            // If this dealer is not in the map yet, add it
            if (!dealerMap.has(dealerId)) {
                dealerMap.set(dealerId, dealer);
                return;
            }

            // If this dealer is already in the map, compare submission dates
            const existingDealer = dealerMap.get(dealerId);

            // Get the latest submission for both dealers
            const existingSubmissions = dealerSelfSubmissions
                .filter(sub => sub.dealerId === existingDealer.dealerId)
                .sort((a, b) => {
                    const dateA = moment(a.reporting_period?.[0], 'MM-YYYY');
                    const dateB = moment(b.reporting_period?.[0], 'MM-YYYY');
                    return dateB - dateA;
                });

            const currentSubmissions = dealerSelfSubmissions
                .filter(sub => sub.dealerId === dealer.dealerId)
                .sort((a, b) => {
                    const dateA = moment(a.reporting_period?.[0], 'MM-YYYY');
                    const dateB = moment(b.reporting_period?.[0], 'MM-YYYY');
                    return dateB - dateA;
                });

            if (existingSubmissions.length === 0 || currentSubmissions.length === 0) {
                return;
            }

            const existingDate = moment(existingSubmissions[0].reporting_period?.[0], 'MM-YYYY');
            const currentDate = moment(currentSubmissions[0].reporting_period?.[0], 'MM-YYYY');

            // Replace the existing dealer if the current one has a more recent submission
            if (currentDate.isAfter(existingDate)) {
                dealerMap.set(dealerId, dealer);
            }
        });

        return Array.from(dealerMap.values());
    };

    // Filter data to only include dealers with valid self-assessment scores and keep only the latest entry for each dealer
    const dealersWithSelfAssessments = groupDealersByIdAndGetLatest(datas);

   

    const RowFilterTemplate = (options, obj) => {
        // Use dealersWithSelfAssessments (filtered data) instead of datas (all data)
        // This ensures filter options only include values from the currently displayed dealers
        return (
            <MultiSelect
                value={options.value}
                options={Array.from(new Set(dealersWithSelfAssessments.map((i) => i[obj]))).filter(x => x)}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                filter
                panelClassName='hidefilter'
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };

    const clearDateFilter = () => {
        setDateFilter({ start: null, end: null });
    };

    const exportExcel = () => {
        if (!datas || datas.length === 0) {
            alert('No data to export.');
            return;
        }

        const exportData = dealersWithSelfAssessments.map((item) => ({
            'S.No': item.tableIndex || '',
            'Calibration ID': item.vendor?.code ? `MSI-${item.vendor.code}-${DateTime.fromISO(item.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}` : 'NA',
            'Dealer Name': item.vendor?.dealerName || 'NA',
            'Location': item.vendor?.dealerLocation || 'NA',
            'Zone': zonalOfficeList.find(x => x.value === item.vendor?.dealerZone)?.name || 'NA',
            'Category': dealerType.find(x => x.value === item.vendor?.dealerCategory)?.name || 'NA',
            'Self-assessment Month': selfAssessmentMonthTemplate(item),
            'Self-assessment Score': selfAssessmentScoreTemplate(item),
            'MSI Rating': getRatingName(selfAssessmentScoreTemplate(item)),
            'Self-assessment Submit Date': selfAssessmentSubmitDateTemplate(item)
        }));

        const worksheet = XLSX.utils.json_to_sheet(exportData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Dealer Self Assessments');

        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
        const dataBlob = new Blob([excelBuffer], { type: 'application/octet-stream' });
        saveAs(dataBlob, `Dealer_Self_Assessments_${moment().format('YYYYMMDD_HHmmss')}.xlsx`);
    };



    return (
        <>
            <div className="col-12 flex justify-content-between align-items-center mb-3" >
                <div className="col-6 flex gap-3 align-items-center">
                    <div className="flex flex-column">
                        <label className="mb-1">Self-assessment Month From</label>
                        <Calendar
                            value={dateFilter.start}
                            onChange={(e) => setDateFilter({ ...dateFilter, start: e.value })}
                            placeholder="Start Date"
                            dateFormat="dd-mm-yy"
                            showIcon
                            view="month"
                            yearNavigator
                            yearRange="2020:2030"
                        />
                    </div>
                    <div className="flex flex-column">
                        <label className="mb-1">To</label>
                        <Calendar
                            value={dateFilter.end}
                            onChange={(e) => setDateFilter({ ...dateFilter, end: e.value })}
                            placeholder="End Date"
                            dateFormat="dd-mm-yy"
                            showIcon
                            view="month"
                            yearNavigator
                            yearRange="2020:2030"
                            minDate={dateFilter.start}
                            disabled={!dateFilter.start}
                        />
                    </div>
                    {(dateFilter.start || dateFilter.end) && (
                        <button
                            className="btn btn-sm btn-outline-secondary align-self-end mb-1"
                            onClick={clearDateFilter}
                            style={{ height: '36px' }}
                        >
                            Clear
                        </button>
                    )}
                </div>
                <div className='col-5'>
                    <span className="p-input-icon-left" style={{ width: '100%' }}>
                        <i className="pi pi-search" />
                        <InputText value={search} style={{ width: '100%' }} onChange={searchFn} placeholder="Search Code/Name" />
                    </span>
                </div>
            </div>
            <div className="d-flex justify-content-between align-items-center mb-3">
                <h4>Latest MSI Self-assessments ({dealersWithSelfAssessments.length})</h4>
                <button
                    className="btn btn-sm btn-success"
                    onClick={exportExcel}
                >
                    Download Excel
                </button>
            </div>

            <DataTable
                value={dealersWithSelfAssessments.map(x=> ({...x,latestSubmission:getLastSubmissionDate(x)}))}
                paginator
                rows={10}
                scrollable
                scrollHeight="500px"
                filters={{
                    dealerName: { matchMode: 'in', value: null },
                    location: { matchMode: 'in', value: null },
                    msiId: { matchMode: 'in', value: null },
                    selfAssessmentGrade: { matchMode: 'in', value: null },
                    zone: { matchMode: 'in', value: null },
                    cat: { matchMode: 'in', value: null }
                }}
                globalFilter={globalFilter}
                className="mt-2 h-500"
            >
                <Column sortable field="tableIndex" header="S.No" body={(rowData, options) => rowData.tableIndex || options.rowIndex + 1} sortFunction={sortIndexColumn} />
                <Column sortable field="msiId" header="Calibration ID" body={calibrationIdBodyTemplate} showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "msiId")
                    } />
                <Column sortable field="dealerName" header="Name" body={nameTemplate} showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "dealerName")
                    } />
                <Column sortable field="location" header="Location" body={locationTemplate} showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "location")
                    } />
                <Column sortable field="zone" header="Zone" body={zoneTemplate} showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "zone")
                    } />
                <Column sortable field="cat" header="Category" body={categoryTemplate} showFilterMatchModes={false}
                    filter
                    filterElement={(options) =>
                        RowFilterTemplate(options, "cat")
                    } />
                <Column sortable field="selfAssessmentMonth" header="Self-assessment Month" body={selfAssessmentMonthTemplate} sortFunction={sortSelfAssessmentMonth} />
                <Column sortable field="selfAssessmentScore" header="MSI Self-assessment Score" body={selfAssessmentScoreTemplate} sortFunction={sortSelfAssessmentScore} />
                <Column field="selfAssessmentGrade" filter showFilterMatchModes={false}
                    filterElement={(options) =>
                        RowFilterTemplate(options, "selfAssessmentGrade")
                    } header="MSI Rating" body={ratingTemplate} />
                <Column sortable field="latestSubmission" header="Self-assessment Submit Date" body={selfAssessmentSubmitDateTemplate} sortFunction={sortSelfAssessmentSubmitDate} />
                <Column
                    header="View Submissions"
                    body={(rowData) => {
                        const matched = dealerSelfSubmissions
                            .filter(sub => sub.dealerId === rowData.dealerId)
                            .sort((a, b) => {
                                const dateA = moment(a.reporting_period?.[0], 'MM-YYYY');
                                const dateB = moment(b.reporting_period?.[0], 'MM-YYYY');
                                return dateB - dateA;
                            });

                        if (matched.length === 0) return <button className="btn btn-sm btn-secondary" disabled>View</button>;

                        return (
                            <button
                                className="btn btn-sm btn-secondary"
                                onClick={() => {
                                    try {
                                        const parsed = JSON.parse(matched[0]?.response || '{}');

                                        // Prepare dealer info for export
                                        const dealerInfo = {
                                            calibrationId: 'MSI-' + (rowData?.vendor?.code || 'NA') + '-' + DateTime.fromISO(rowData.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy'),
                                            dealerName: rowData?.vendor?.dealerName || 'NA',
                                            location: rowData?.vendor?.dealerLocation || 'NA',
                                            zone: zonalOfficeList.find(x => x.value === rowData?.vendor?.dealerZone)?.name || 'NA',
                                            category: dealerType.find(x => x.value === rowData?.vendor?.dealerCategory)?.name || 'NA',
                                            selfAssessmentMonth: selfAssessmentMonthTemplate(rowData),
                                            selfAssessmentScore: selfAssessmentScoreTemplate(rowData),
                                            msiRating: getRatingName(selfAssessmentScoreTemplate(rowData)),
                                            selfAssessmentSubmitDate: selfAssessmentSubmitDateTemplate(rowData)
                                        };

                                        setSubmissionData(parsed);
                                        setDealerInfo(dealerInfo); // Store dealer info
                                        setSubmissionDialog(true);
                                    } catch (err) {
                                        console.error('Invalid JSON in response', err);
                                        alert('Invalid submission data');
                                    }
                                }}
                            >
                                View
                            </button>
                        );
                    }}
                />
            </DataTable>

            <Dialog
                visible={submissionDialog}
                onHide={() => {
                    setSubmissionDialog(false);
                    setDealerInfo(null); // Clear dealer info when dialog is closed
                }}
                style={{ width: '80vw' }}
                className="custom-dialog"
            >
                {submissionData ? (
                    <DealerSubmissionView excelData={submissionData} dealerInfo={dealerInfo} />
                ) : (
                    <p>No submission data available.</p>
                )}
            </Dialog>
        </>
    );
};

export default DealersSelfAssessmentTable;
