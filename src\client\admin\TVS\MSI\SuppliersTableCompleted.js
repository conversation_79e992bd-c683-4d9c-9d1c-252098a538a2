import React, { useEffect, useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { DateTime } from 'luxon';
import SupplierReport from '../SupplierScreen/SupplierReport';
import { Dialog } from 'primereact/dialog';
import useForceUpdate from 'use-force-update';
import SupplierPanel from '../SupplierScreen/SupplierPanel';
import AuditPanel from '../Auditor/AuditPanel';
import { InputText } from 'primereact/inputtext';
import { MultiSelect } from 'primereact/multiselect';
import APIServices from '../../../../service/APIService';
import { API } from '../../../../constants/api_url';
import Swal from 'sweetalert2';
import DocViewer, { DocViewerRenderers } from "@cyntler/react-doc-viewer";
import "@cyntler/react-doc-viewer/dist/index.css";
import ActionOfStatus from './ActionOfStatus';
import { Calendar } from 'primereact/calendar';
import moment from 'moment';
import './MSIStyles.css';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import AddActionDialog from './components/AddActionDialog';
import { Badge } from 'primereact/badge';
import { useSelector } from 'react-redux';

const SuppliersTableCompleted = ({ data, assessorList, globalFilter, supplierList, editSupplier }) => {
    const login_data = useSelector((state) => state.user.userdetail);

    const [attdialog, setAttDialog] = useState(false);
    const [attachment, setAttachment] = useState([]);
    const [search, setSearch] = useState('')
    const [databk, setDatabk] = useState([])
    const [datas, setDatas] = useState([])
    const [dateFilter, setDateFilter] = useState({ start: null, end: null })
    const [selectedAttachments, setSelectedAttachments] = useState([])
    const [docdialog, setDocDialog] = useState(false)
    const [reportdialog, setReportDialog] = useState(false)
    const [selectedAudit, setSelectedAudit] = useState(null)
    const forceUpdate = useForceUpdate();
    const [actionModal, setActionModal] = useState(false)
    const [actionModal2, setActionModal2] = useState(false)
    const [actionReportData, setActionReportData] = useState([])
    const [actionStatusReport, setActionStatusReport] = useState(false)
    const [addActionDialogVisible, setAddActionDialogVisible] = useState(false)
    const [selectedSupplier, setSelectedSupplier] = useState(null)

    // Add state for table filters
    const [tableFilters, setTableFilters] = useState({
        supplierName: { matchMode: 'in', value: null },
        location: { matchMode: 'in', value: null },
        msiId: { matchMode: 'in', value: null },
        cat: { matchMode: 'in', value: null },
        stat: { matchMode: 'in', value: null },
        team1Members: { matchMode: 'in', value: null },
        team2Members: { matchMode: 'in', value: null },
        team3Members: { matchMode: 'in', value: null },
        team4Members: { matchMode: 'in', value: null }
    })

    const getActionsStatus = (data) => {
        if (!Array.isArray(data)) return "0/0";

        const category3Items = data.filter(item => item.categoryOfFinding === 3);
        const totalCategory3 = category3Items.length;

        if (totalCategory3 === 0) return "0/0";

        const type3Count = category3Items.filter(item => item.nonComplianceType === 3).length;
        return `${type3Count}/${totalCategory3}`;
    }

    // Helper function for export to get action plan status text for all actions
    const getActionPlanStatus = (rowData) => {
        if (!Array.isArray(rowData.supplierActions) || rowData.supplierActions.length === 0) {
            return '-';
        }

        // Check if any actions are in different statuses
        const hasPendingSubmission = rowData.supplierActions.some(action => !action.status || action.status === 'initiated');
        const hasUnderReview = rowData.supplierActions.some(action => action.status === 'under_review');
        const hasApproved = rowData.supplierActions.some(action => action.status === 'approved');

        // Create status text with line breaks
        let statusText = [];
        if (hasPendingSubmission) statusText.push('Pending submission');
        if (hasUnderReview) statusText.push('Under TVS review');
        if (hasApproved) statusText.push('Approved');

        return statusText.join(', ');
    }

    // This function is no longer used, but we're keeping the getActionPlanStatus function
    const exportExcel = () => {
        if (!datas || datas.length === 0) {
            alert('No data available to export.');
            return;
        }

        const exportData = datas.map((item) => ({
            'S.No': item.tableIndex || '',
            'Calibration ID': item.vendorCode ? `MSI-${item.vendorCode}-${DateTime.fromISO(item.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}` : 'NA',
            'Supplier Name': item.vendor?.supplierName || 'NA',
            'Supplier Location': item.vendor?.supplierLocation || 'NA',
            'Supplier Contact': supplierList.find(i => i.id === item.supplierId)?.information?.supplierContact || 'NA',
            'Category': categoryList.find(x => x.value === item?.vendor?.supplierCategory)?.name || 'NA',
            'Self-Assessment Submission Date': item?.supplierAssignmentSubmission?.submitted_on
                ? DateTime.fromISO(item?.supplierAssignmentSubmission?.submitted_on, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy')
                : 'NA',
            'MSI Self-Assessment Score': item?.supplierAssignmentSubmission?.supplierMSIScore || 'NA',
            'Calibration Start Date': item.auditStartDate
                ? DateTime.fromISO(item.auditStartDate, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy')
                : 'Not Set',
            'Calibration End Date': item.auditEndDate
                ? DateTime.fromISO(item.auditEndDate, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy')
                : 'Not Set',
            'MSI Auditor Score': item?.auditorAssignmentSubmission?.auditorMSIScore || 'NA',
            'MSI Rating': item.stat || 'NA',
            'No. of Actions Assigned': Array.isArray(item.supplierActions) ?
                item.supplierActions.filter(action => action.categoryOfFinding === 3).length : 0,
            'Action Plan Status': getActionPlanStatus(item),
            'Actions Awaiting Supplier Response': Array.isArray(item.supplierActions) ?
                item.supplierActions.filter(action =>
                    action.categoryOfFinding === 3 && (action.type === 1 || action.type === null)
                ).length : 0,
            'Actions to be Reviewed by TVS': Array.isArray(item.supplierActions) ?
                item.supplierActions.filter(action =>
                    action.categoryOfFinding === 3 && action.type === 2
                ).length : 0,
            'Closed Actions': Array.isArray(item.supplierActions) ?
                item.supplierActions.filter(action =>
                    action.categoryOfFinding === 3 && action.type === 3
                ).length : 0,
            'Calibration Team Member 1': item.team1Members?.join(', ') || 'Not Assigned',
            'Calibration Team Member 2': item.team2Members?.join(', ') || 'Not Assigned',
            'Calibration Team Member 3': item.team3Members?.join(', ') || 'Not Assigned',
            'Calibration Team Member 4': item.team4Members?.join(', ') || 'Not Assigned'
        }));

        const worksheet = XLSX.utils.json_to_sheet(exportData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Suppliers Completed');

        const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
        const dataBlob = new Blob([excelBuffer], { type: 'application/octet-stream' });
        saveAs(dataBlob, `Suppliers_Completed_List_${moment().format('YYYYMMDD_HHmmss')}.xlsx`);
    };
  const sendReportTemplate = (rowData) => {
        const show = rowData?.auditorAssignmentSubmission?.type === 2 || false
        const count = Array.isArray(rowData?.auditorAssignmentSubmission?.reportMailStatus) ? rowData?.auditorAssignmentSubmission?.reportMailStatus?.length  : 0
     if(rowData?.auditorAssignmentSubmission?.reportMailStatus){
        console.log(rowData?.auditorAssignmentSubmission?.reportMailStatus)
     }
     
     return show ? <div>
             <i className='pi pi-envelope p-overlay-badge' style={{fontSize:'1.5rem'}} onClick={() => {

            // Show confirmation dialog
            Swal.fire({
                title: 'Send Report',
                text: 'Are you sure you want to send this report?',
                icon: 'question',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes, send it!'
            }).then(async (result) => {
                if (result.isConfirmed) {
                    // User confirmed, proceed with sending the report
                    const mailStatus = await APIServices.post(API.SendReportToSupplier(rowData?.auditorAssignmentSubmission.id), { requestId: login_data.id })

                    // Show success/error message
                    Swal.fire({
                        position: 'center',
                        title: mailStatus?.data?.message || 'Something went wrong, try again',
                        showConfirmButton: false,
                        timer: 1500
                    })
                }
            })

        }} >

    <Badge value={count}></Badge>
</i>


            </div> : <div>NA</div>
    }
    // Define applyFilters function before using it in useEffect
    const applyFilters = (dataToFilter, searchValue = search) => {
        // Apply search filter
        let filteredData = dataToFilter;
        if (searchValue) {
            filteredData = filteredData.filter(x =>
                x?.vendor?.supplierName?.trim().toLowerCase().includes(searchValue?.trim().toLowerCase()) ||
                x?.vendor?.code?.trim().toLowerCase().includes(searchValue?.trim().toLowerCase())
            );
        }

        // Apply date range filter
        if (dateFilter.start && dateFilter.end) {
            filteredData = filteredData.filter(rowData => {
                const dateStr = rowData?.auditStartDate;
                if (!dateStr) return true;

                const itemDate = DateTime.fromISO(dateStr, { zone: 'utc' }).toJSDate();
                const startDate = moment(dateFilter.start).startOf('day').toDate();
                const endDate = moment(dateFilter.end).endOf('day').toDate();

                return itemDate >= startDate && itemDate <= endDate;
            });
        }

        // Add tableIndex property for sorting
        const indexedData = filteredData.map((item, index) => ({
            ...item,
            tableIndex: index + 1
        }));

        setDatas(indexedData);
    };

    useEffect(() => {
        console.count('data')
        // Add statusofaction to each item and prepare category data for filtering
        const updatedData = data.map(item => {
            const categoryName = categoryList.find(i => i.value === item?.vendor?.supplierCategory)?.name || 'Not Found';

            const score = item?.auditorAssignmentSubmission?.auditorMSIScore ?? null;
            let ratingLabel = 'NA';

            if (score !== null) {
                if (score >= 85) ratingLabel = 'Platinum';
                else if (score > 70) ratingLabel = 'Gold';
                else if (score > 55) ratingLabel = 'Silver';
                else ratingLabel = 'Not Met';
            }

            return {
                ...item,
                statusOfActions: getActionsStatus(item.supplierActions),
                cat: categoryName,
                stat: ratingLabel // ✅ Add MSI rating here
            };
        });


        setDatabk(updatedData);
        applyFilters(updatedData);
        // eslint-disable-next-line react-hooks/exhaustive-deps
        console.log("filter Effect")
    }, [data]);
useEffect(()=>{
        console.count('mount')

},[])
    useEffect(() => {

        applyFilters(databk);
        // eslint-disable-next-line react-hooks/exhaustive-deps
        console.log("hk")
    }, [dateFilter]);

    const categoryList = [{ name: 'Forging & Machining', value: 1 }, { name: 'Casting & Machining', value: 2 }, { name: 'Pressing & Fabrication', value: 3 }, { name: 'Proprietary Mechanical', value: 4 }, { name: 'Proprietary Electrical', value: 5 }, { name: 'Plastics, Rubber, Painting and Stickers', value: 6 }, { name: 'EV/3W/2W', value: 7 }, { name: 'BW', value: 8 }, { name: 'Accessories', value: 9 }, { name: 'IDM (Indirect Material)', value: 10 }, { name: 'Import', value: 11 }]
    const calibrationIdBodyTemplate = (rowData) => {

    console.log('calibrationnn')
        return (
            <>
                {rowData?.auditorAssignmentSubmission?.type === 2 ?

                    <label className='clr-navy fw-7 fs-14 cur-pointer text-underline' onClick={(e) => { e.preventDefault(); openReportDialog(rowData); }}>
                        {'MSI-' + (rowData?.vendorCode || 'NA') + '-' + DateTime.fromISO(rowData.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}
                    </label>
                    : !rowData?.assessmentEndDate ?
                        <label className={rowData?.attachment2?.length ? 'clr-navy fw-7 fs-14 cur-pointer text-underline' : ''} onClick={(e) => { e.preventDefault(); if (rowData?.attachment2?.length) { openDocDialog(rowData?.attachment2); } }}>
                            {'MSI-' + (rowData?.vendorCode || 'NA') + '-' + DateTime.fromISO(rowData.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}
                        </label>
                        :
                        <label>
                            {'MSI-' + (rowData?.vendorCode || 'NA') + '-' + DateTime.fromISO(rowData.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')}
                        </label>
                }
            </>

        );
    };
    const supplierScoreTemplate = (rowData) => {
        let finished = rowData?.supplierAssignmentSubmission?.type === 1

        return (
            <div className={finished ? 'clr-navy fw-6 fs-14 cur-pointer text-underline' : 'fw-5 fs-14'} onClick={(e) => { e.preventDefault(); if (finished) { openActionModal(rowData); } }}>
                {rowData?.supplierAssignmentSubmission?.supplierMSIScore ? rowData?.supplierAssignmentSubmission?.supplierMSIScore : 'NA'}
            </div>
        );
    }
    const getCalibirationId = (rowData) => {
        return 'MSI-' + (rowData?.vendorCode || 'NA') + '-' + DateTime.fromISO(rowData.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy')
    }
    const auditorScoreTemplate = (rowData) => {
        let finished = ((rowData?.auditorAssignmentSubmission?.type === 1 || rowData?.auditorAssignmentSubmission?.type === 2) && rowData?.assessmentEndDate) || (!rowData?.assessmentEndDate && rowData?.attachment1?.length)

        return (
            <div className={finished ? 'clr-navy fw-6 fs-14 cur-pointer text-underline' : 'fw-5 fs-14'} onClick={(e) => {
                e.preventDefault();
                if (finished) {
                    if (rowData?.attachment1?.length) {
                        openDocDialog(rowData?.attachment1);
                    } else {
                        openActionModal2(rowData);
                    }
                }
            }}>
                {rowData?.auditorAssignmentSubmission?.auditorMSIScore ? rowData?.auditorAssignmentSubmission?.auditorMSIScore : 'NA'}
            </div>
        );
    }
    const reportTemplate = (rowData) => {
        return (rowData?.auditorAssignmentSubmission?.type === 1 ? <div onClick={() => openReportDialog(rowData)}>View  </div> : 'NA')
    }
    const nameTemplate = (rowData) => {

        return (

            rowData?.vendor?.supplierName || 'NA'

        );
    };
    const searchFn = (e) => {
        let val = e.target.value
        setSearch(val)
        applyFilters(databk, val)
    }

    const clearDateFilter = () => {
        setDateFilter({ start: null, end: null });
    };
    const locationTemplate = (rowData) => {
        return (
            rowData?.vendor?.supplierLocation || 'NA'
        );
    };
    const categoryTemplate = (rowData) => {
        console.log(rowData)
        return (

            categoryList.find(i => i.value === rowData?.vendor?.supplierCategory)?.name || 'Not Found'

        );
    };
    const assessmentDueDate = (rowData) => {
        return (

            DateTime.fromISO(rowData?.supplierAssignmentSubmission?.submitted_on, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy')

        );
    };
    const calibrationStartDate = (rowData) => {
        return (

            rowData.auditStartDate ? DateTime.fromISO(rowData.auditStartDate, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy') : 'Not Set'

        );
    };
    const calibrationEndDate = (rowData) => {
        return (

            rowData.auditEndDate ? DateTime.fromISO(rowData.auditEndDate, { zone: 'utc' }).toLocal().toFormat('dd-MM-yyyy') : 'Not Set'


        );
    };
    const ratingTemplate = (rowData) => {

        const score = rowData?.auditorAssignmentSubmission?.auditorMSIScore || null
        let ratingLabel = 'NA';

        if (score !== null && score !== undefined) {
            if (score >= 85) ratingLabel = 'Platinum';
            else if (score > 70) ratingLabel = 'Gold';
            else if (score > 55) ratingLabel = 'Silver';
            else ratingLabel = 'Not Met';
        }

        // Store the rating label in the rowData for filtering
        rowData.stat = ratingLabel;

        return (
            <div style={{ width: score > 55 ? 50 : 80 }}>
                {score != null ? score >= 85 ? <img width={'100%'} alt="Platinum Rating" src={require('../../../../assets/images/report/valuechain/platinum_rating.png').default} /> : score > 70 ? <img width={'100%'} alt="Gold Rating" src={require('../../../../assets/images/report/valuechain/gold_rating.png').default} /> : score > 55 ? <img width={'100%'} alt="Silver Rating" src={require('../../../../assets/images/report/valuechain/silver_rating.png').default} /> : "Not Met" : "NA"}
            </div>
        )
    }
    const contactTemplate = (rowData) => {
        return (

            supplierList.find(i => i.id === rowData.supplierId)?.information?.supplierContact || 'NA'

        );
    };
    const pySpendBodyTemplate = (rowData) => {
        return `${supplierList.find(i => i.id === rowData.supplierId)?.information?.supplierSpentOn || '- '} Cr. INR`;
    };

    // Number of Actions assigned (Non-Compliance only)
    const actionsAssignedTemplate = (rowData) => {
        if (!Array.isArray(rowData.supplierActions)) {
            return <span>0</span>;
        }

        // Count only Non-Compliance actions (category 3)
        const nonComplianceActions = rowData.supplierActions.filter(item => item.categoryOfFinding === 3);
        const totalNonCompliance = nonComplianceActions.length;

        return (
            <span
                className="clr-navy fw-6 fs-14 cur-pointer text-underline"
                onClick={() => dealerActionReport(rowData)}
            >
                {totalNonCompliance}
            </span>
        );
    };

    // Action Plan Status (Non-Compliance only)
    const actionPlanStatusTemplate = (rowData) => {
        if (!Array.isArray(rowData.supplierActions)) {
            return <span>-</span>;
        }

        // Filter for Non-Compliance actions only
        const nonComplianceActions = rowData.supplierActions.filter(item => item.categoryOfFinding === 3);

        if (nonComplianceActions.length === 0) {
            return <span>-</span>;
        }

        // Check if any actions are in different statuses
        const hasPendingSubmission = nonComplianceActions.some(action => !action.status || action.status === 'initiated');
        const hasUnderReview = nonComplianceActions.some(action => action.status === 'under_review');
        const hasApproved = nonComplianceActions.some(action => action.status === 'approved');

        // Create status text with line breaks
        let statusText = [];
        if (hasPendingSubmission) statusText.push('Pending submission');
        if (hasUnderReview) statusText.push('Under TVS review');
        if (hasApproved) statusText.push('Approved');

        return (
            <div
                style={{ cursor: 'pointer' }}
                onClick={() => dealerActionReport(rowData)}
            >
                {statusText.map((text, index) => (
                    <div key={index}>{text}</div>
                ))}
            </div>
        );
    };

    // Actions Awaiting Supplier Response (type 1)
    const actionsAwaitingSupplierResponseTemplate = (rowData) => {
        if (!Array.isArray(rowData.supplierActions)) {
            return <span>0</span>;
        }

        // Filter for Non-Compliance actions only (category 3)
        const nonComplianceActions = rowData.supplierActions.filter(item => item.categoryOfFinding === 3);

        // Count actions with type 1 (Action Assigned)
        const awaitingSupplierResponseCount = nonComplianceActions.filter(action =>
            (action.type === 1 || action.type === null)
        ).length;

        return (
            <span
                className="clr-navy fw-6 fs-14 cur-pointer text-underline"
                onClick={() => dealerActionReport(rowData)}
            >
                {awaitingSupplierResponseCount}
            </span>
        );
    };

    // Actions to be Reviewed by TVS (type 2)
    const actionsToBeReviewedTemplate = (rowData) => {
        if (!Array.isArray(rowData.supplierActions)) {
            return <span>0</span>;
        }

        // Filter for Non-Compliance actions only (category 3)
        const nonComplianceActions = rowData.supplierActions.filter(item => item.categoryOfFinding === 3);

        // Count actions with type 2 (Action Returned)
        const toBeReviewedCount = nonComplianceActions.filter(action =>
            action.type === 2
        ).length;

        return (
            <span
                className="clr-navy fw-6 fs-14 cur-pointer text-underline"
                onClick={() => dealerActionReport(rowData)}
            >
                {toBeReviewedCount}
            </span>
        );
    };

    // Closed Actions (type 3 - Action Completed)
    const closedActionsTemplate = (rowData) => {
        if (!Array.isArray(rowData.supplierActions)) {
            return <span>0</span>;
        }

        // Filter for Non-Compliance actions only (category 3)
        const nonComplianceActions = rowData.supplierActions.filter(item => item.categoryOfFinding === 3);

        // Count actions with type 3 (Action Completed)
        const completedCount = nonComplianceActions.filter(action =>
            action.type === 3
        ).length;

        return (
            <span
                className="clr-navy fw-6 fs-14 cur-pointer text-underline"
                onClick={() => dealerActionReport(rowData)}
            >
                {completedCount}
            </span>
        );
    };

    // Create a memoized function to open the action status report
    const openActionStatusReport = React.useCallback((rowData) => {
        setActionReportData(rowData);
        setActionStatusReport(true);
    }, []);

    const dealerActionReport = (rowData) => {
        openActionStatusReport(rowData);
    }

    // Create a memoized function to open the add action dialog
    const openAddActionDialog = React.useCallback((supplier) => {
        setSelectedSupplier(supplier);
        setAddActionDialogVisible(true);
    }, []);

    // Add Action template
    const addActionTemplate = (rowData) => {
        return (
            <div className="flex justify-content-center">
                <button
                    className="p-button p-button-rounded p-button-text"
                    onClick={() => openAddActionDialog(rowData)}
                >
                    <i className="pi pi-plus-circle" style={{ color: '#315975' }}></i>
                </button>
            </div>
        );
    };

    // Handle action added callback
    const handleActionAdded = (updatedSupplier) => {
                // Update the data in the table
               
        const updatedData = datas.map(item =>
            item.id === updatedSupplier.id ? updatedSupplier : item
        );
        setDatas(updatedData);

        // Also update the backup data
        const updatedBackupData = databk.map(item =>
            item.id === updatedSupplier.id ? updatedSupplier : item
        );
        setDatabk(updatedBackupData);
    };

    // Create memoized dialog open functions to prevent unnecessary re-renders
    const openAttDialog = React.useCallback((attachments) => {
        setAttachment(attachments);
        setAttDialog(true);
    }, []);

    const openDocDialog = React.useCallback((attachments) => {
        setSelectedAttachments(attachments);
        setDocDialog(true);
    }, []);

    const openReportDialog = React.useCallback((audit) => {
        setSelectedAudit(audit);
        setReportDialog(true);
    }, []);

    const openActionModal = React.useCallback((audit) => {
        setSelectedAudit(audit);
        setActionModal(true);
    }, []);

    const openActionModal2 = React.useCallback((audit) => {
        setSelectedAudit(audit);
        setActionModal2(true);
    }, []);

    const team1Template = (rowData) => {
        // Store the team members in rowData for filtering
        const teamMembers = rowData?.group1?.assessors?.length
            ? rowData?.group1?.assessors
                .map(x => assessorList.find(i => i.id === x))
                .filter(i => i)
                .map(i => i?.information?.empname || '')
                .filter(name => name !== '')
            : [];

        // Store the team members in rowData for filtering
        rowData.team1Members = teamMembers;

        // Return a formatted string for display
        if (teamMembers.length > 0) {
            return teamMembers.join(', ');
        } else {
            return 'Not Assigned';
        }
    }

    const team2Template = (rowData) => {
        // Store the team members in rowData for filtering
        const teamMembers = rowData?.group2?.assessors?.length
            ? rowData?.group2?.assessors
                .map(x => assessorList.find(i => i.id === x))
                .filter(i => i)
                .map(i => i?.information?.empname || '')
                .filter(name => name !== '')
            : [];

        // Store the team members in rowData for filtering
        rowData.team2Members = teamMembers;

        // Return a formatted string for display
        if (teamMembers.length > 0) {
            return teamMembers.join(', ');
        } else {
            return 'Not Assigned';
        }
    }

    const team3Template = (rowData) => {
        // Store the team members in rowData for filtering
        const teamMembers = rowData?.group3?.assessors?.length
            ? rowData?.group3?.assessors
                .map(x => assessorList.find(i => i.id === x))
                .filter(i => i)
                .map(i => i?.information?.empname || '')
                .filter(name => name !== '')
            : [];

        // Store the team members in rowData for filtering
        rowData.team3Members = teamMembers;

        // Return a formatted string for display
        if (teamMembers.length > 0) {
            return teamMembers.join(', ');
        } else {
            return 'Not Assigned';
        }
    }

    const team4Template = (rowData) => {
        // Store the team members in rowData for filtering
        const teamMembers = rowData?.group4?.assessors?.length
            ? rowData?.group4?.assessors
                .map(x => assessorList.find(i => i.id === x))
                .filter(i => i)
                .map(i => i?.information?.empname || '')
                .filter(name => name !== '')
            : [];

        // Store the team members in rowData for filtering
        rowData.team4Members = teamMembers;

        // Return a formatted string for display
        if (teamMembers.length > 0) {
            return teamMembers.join(', ');
        } else {
            return 'Not Assigned';
        }
    }
    const handleCalibrationClick = (rowData) => {
        // Logic when calibration ID is clicked
        alert('Clicked on: ' + rowData.calibrationId);
    };
    const actionBodytemplate = (rowData) => {
        return (
            <div>
                <button
                    className="btn btn-sm btn-primary"
                    onClick={() => editSupplier(rowData)}
                >
                    <i className="pi pi-pencil" />
                </button>
            </div>
        )
    }
    const sortAuditStartDate = (e) => {
        console.log(e.data);
        if (e.order === 1) {
            return e.data.sort((a, b) => {
                let dateA = DateTime.fromISO(a.auditStartDate, { zone: 'utc' });
                let dateB = DateTime.fromISO(b.auditStartDate, { zone: 'utc' });


                // Compare the parsed dates
                if (dateA < dateB) return -1;
                if (dateA > dateB) return 1;
                return 0;
            });
        } else {
            return e.data.sort((a, b) => {
                let dateA = DateTime.fromISO(a.auditStartDate, { zone: 'utc' });
                let dateB = DateTime.fromISO(b.auditStartDate, { zone: 'utc' });
                // Compare the parsed dates in descending order
                if (dateA > dateB) return -1;
                if (dateA < dateB) return 1;
                return 0;
            });
        }
    };
    const sortAuditEndDate = (e) => {
        console.log(e.data);
        if (e.order === 1) {
            return e.data.sort((a, b) => {
                let dateA = DateTime.fromISO(a.auditEndDate, { zone: 'utc' });
                let dateB = DateTime.fromISO(b.auditEndDate, { zone: 'utc' });


                // Compare the parsed dates
                if (dateA < dateB) return -1;
                if (dateA > dateB) return 1;
                return 0;
            });
        } else {
            return e.data.sort((a, b) => {
                let dateA = DateTime.fromISO(a.auditEndDate, { zone: 'utc' });
                let dateB = DateTime.fromISO(b.auditEndDate, { zone: 'utc' });
                // Compare the parsed dates in descending order
                if (dateA > dateB) return -1;
                if (dateA < dateB) return 1;
                return 0;
            });
        }
    };

    // Sort function for Calibration ID
    const sortCalibrationId = (e) => {
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const idA = 'MSI-' + (a?.vendorCode || 'NA') + '-' + DateTime.fromISO(a.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy');
                const idB = 'MSI-' + (b?.vendorCode || 'NA') + '-' + DateTime.fromISO(b.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy');
                return idA.localeCompare(idB);
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const idA = 'MSI-' + (a?.vendorCode || 'NA') + '-' + DateTime.fromISO(a.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy');
                const idB = 'MSI-' + (b?.vendorCode || 'NA') + '-' + DateTime.fromISO(b.created_on, { zone: 'utc' }).toLocal().toFormat('ddMMyyyy');
                return idB.localeCompare(idA);
            });
        }
    };

    // Sort function for Name
    const sortName = (e) => {
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const nameA = a?.vendor?.supplierName || 'NA';
                const nameB = b?.vendor?.supplierName || 'NA';
                return nameA.localeCompare(nameB);
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const nameA = a?.vendor?.supplierName || 'NA';
                const nameB = b?.vendor?.supplierName || 'NA';
                return nameB.localeCompare(nameA);
            });
        }
    };

    // Sort function for Location
    const sortLocation = (e) => {
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const locationA = a?.vendor?.supplierLocation || 'NA';
                const locationB = b?.vendor?.supplierLocation || 'NA';
                return locationA.localeCompare(locationB);
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const locationA = a?.vendor?.supplierLocation || 'NA';
                const locationB = b?.vendor?.supplierLocation || 'NA';
                return locationB.localeCompare(locationA);
            });
        }
    };

    // Sort function for Category
    const sortCategory = (e) => {
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const categoryA = categoryList.find(i => i.value === a?.vendor?.supplierCategory)?.name || 'Not Found';
                const categoryB = categoryList.find(i => i.value === b?.vendor?.supplierCategory)?.name || 'Not Found';
                return categoryA.localeCompare(categoryB);
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const categoryA = categoryList.find(i => i.value === a?.vendor?.supplierCategory)?.name || 'Not Found';
                const categoryB = categoryList.find(i => i.value === b?.vendor?.supplierCategory)?.name || 'Not Found';
                return categoryB.localeCompare(categoryA);
            });
        }
    };

    // Sort function for Self Assessment Submit Date
    const sortSelfAssessmentDate = (e) => {
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const dateA = a?.supplierAssignmentSubmission?.submitted_on ? DateTime.fromISO(a.supplierAssignmentSubmission.submitted_on, { zone: 'utc' }) : null;
                const dateB = b?.supplierAssignmentSubmission?.submitted_on ? DateTime.fromISO(b.supplierAssignmentSubmission.submitted_on, { zone: 'utc' }) : null;

                if (!dateA && !dateB) return 0;
                if (!dateA) return 1; // null dates at the end for ascending
                if (!dateB) return -1;

                if (dateA < dateB) return -1;
                if (dateA > dateB) return 1;
                return 0;
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const dateA = a?.supplierAssignmentSubmission?.submitted_on ? DateTime.fromISO(a.supplierAssignmentSubmission.submitted_on, { zone: 'utc' }) : null;
                const dateB = b?.supplierAssignmentSubmission?.submitted_on ? DateTime.fromISO(b.supplierAssignmentSubmission.submitted_on, { zone: 'utc' }) : null;

                if (!dateA && !dateB) return 0;
                if (!dateA) return 1; // null dates at the end for descending too
                if (!dateB) return -1;

                if (dateA > dateB) return -1;
                if (dateA < dateB) return 1;
                return 0;
            });
        }
    };

    // Sort function for MSI Self Assessment Score
    const sortSelfAssessmentScore = (e) => {
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const scoreA = a?.supplierAssignmentSubmission?.supplierMSIScore || null;
                const scoreB = b?.supplierAssignmentSubmission?.supplierMSIScore || null;

                if (scoreA === null && scoreB === null) return 0;
                if (scoreA === null) return 1; // null scores at the end for ascending
                if (scoreB === null) return -1;

                return scoreA - scoreB;
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const scoreA = a?.supplierAssignmentSubmission?.supplierMSIScore || null;
                const scoreB = b?.supplierAssignmentSubmission?.supplierMSIScore || null;

                if (scoreA === null && scoreB === null) return 0;
                if (scoreA === null) return 1; // null scores at the end for descending too
                if (scoreB === null) return -1;

                return scoreB - scoreA;
            });
        }
    };

    // Sort function for MSI Score
    const sortMSIScore = (e) => {
        if (e.order === 1) { // ascending
            return e.data.sort((a, b) => {
                const scoreA = a?.auditorAssignmentSubmission?.auditorMSIScore || null;
                const scoreB = b?.auditorAssignmentSubmission?.auditorMSIScore || null;

                if (scoreA === null && scoreB === null) return 0;
                if (scoreA === null) return 1; // null scores at the end for ascending
                if (scoreB === null) return -1;

                return scoreA - scoreB;
            });
        } else { // descending
            return e.data.sort((a, b) => {
                const scoreA = a?.auditorAssignmentSubmission?.auditorMSIScore || null;
                const scoreB = b?.auditorAssignmentSubmission?.auditorMSIScore || null;

                if (scoreA === null && scoreB === null) return 0;
                if (scoreA === null) return 1; // null scores at the end for descending too
                if (scoreB === null) return -1;

                return scoreB - scoreA;
            });
        }
    };
    const RowFilterTemplate = (options, obj) => {
console.log('testtt',options,obj)
        return (
            <MultiSelect
                value={options.value}
                options={Array.from(new Set(data.map((i) => i[obj]))).filter(x => x)}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                filter
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };

    const RowFilterTemplate2 = (options, obj) => {

        return (
            <MultiSelect
                value={options.value}
                options={Array.from(new Set(datas.map((i) => i[obj]))).filter(x => x)}
                onChange={(e) => options.filterCallback(e.value)}
                placeholder="Any"
                filter
                panelClassName='hidefilter'
                className="p-column-filter"
                maxSelectedLabels={1}
                style={{ minWidth: "14rem" }}
            />
        );
    };

    const handleReportAttachmentUpload = (e, id, obj) => {
        let allowedext = ['.pdf', '.PDF', '.xls', '.xlsx', '.doc', '.docx']
        const file = e.target.files[0]
        let filtered_files = allowedext.includes(file.name.substr(file.name.lastIndexOf('.')))

        let formData = new FormData()
        let count = 0
        formData.append('file', file)
        console.log(filtered_files)
        e.target.value = null
        if (filtered_files) {

            APIServices.post(API.FilesUpload, formData, {
                headers: {
                    'content-type': 'multipart/form-data'

                }
            }).then((res) => {

                APIServices.patch(API.SupplierAssessmentAss_Edit(id), { [obj]: res.data.files }).then((res2) => {

                    let loc = JSON.parse(JSON.stringify(datas))
                    let loc2 = JSON.parse(JSON.stringify(databk))
                    let index = loc.findIndex(i => i.id === id)
                    if (index !== -1) {
                        if (obj === 'attachment3') {
                            if (loc[index]['attachment3']) {
                                loc[index]['attachment3'].push(...res.data.files)
                            } else {
                                loc[index] = { ...loc[index], [obj]: res.data.files }
                            }
                        } else {
                            loc[index] = { ...loc[index], [obj]: res.data.files }
                        }

                        setDatas(loc)
                    }
                    let index2 = loc2.findIndex(i => i.id === id)
                    if (index2 !== -1) {
                        if (obj === 'attachment3') {
                            if (loc2[index]['attachment3']) {
                                loc2[index]['attachment3'].push(...res.data.files)
                            } else {
                                loc2[index] = { ...loc2[index], [obj]: res.data.files }
                            }
                        } else {
                            loc2[index] = { ...loc2[index], [obj]: res.data.files }
                        }

                        setDatabk(loc2)
                    }

                })

            }).catch((e) => {
                Swal.fire({
                    position: "center",
                    icon: "warning",
                    title: "Something went wrong while uploading file",
                    showConfirmButton: false,
                    timer: 2000,
                });
            })
        } else {

            Swal.fire({
                position: "center",
                icon: "warning",
                title: "invalid file format, supported format PDF,Word & Excel only",
                showConfirmButton: false,
                timer: 2000,
            });
        }
    }
    const handleRemoveAttachment = (id, obj, index_) => {

        let loc = JSON.parse(JSON.stringify(datas))
        let loc2 = JSON.parse(JSON.stringify(databk))
        let index = loc.findIndex(i => i.id === id)
        let index2 = loc2.findIndex(i => i.id === id)
        let attachment = []
        if (index_ != null) {
            let att = loc[index][obj]

            att.splice(index_, 1)
            attachment = att

        }
        APIServices.patch(API.SupplierAssessmentAss_Edit(id), { [obj]: attachment }).then((res2) => {


            if (index !== -1) {
                loc[index] = { ...loc[index], [obj]: attachment }
                if (index_ != null) {
                    setAttachment(loc[index])
                    if (!attachment.length) {
                        setAttDialog(false)
                    }
                }

                setDatas(loc)
            }

            if (index2 !== -1) {
                loc2[index] = { ...loc2[index], [obj]: attachment }
                setDatabk(loc2)
            }

        })


    }
    const caliAttTemplate = (rowData) => {
        return !rowData.assessmentEndDate ? rowData?.attachment1?.length ? <span className='mandatory cur-pointer text-underline ' onClick={() => { handleRemoveAttachment(rowData.id, 'attachment1') }}>Remove</span> : <label htmlFor={rowData.id + 'one'} className='cur-pointer clr-navy text-underline fw-7' >Add <input type='file' accept=".pdf,.xls,.xlsx,.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            id={rowData.id + 'one'} hidden onChange={(e) => { handleReportAttachmentUpload(e, rowData.id, 'attachment1') }} ></input> </label> : 'NA'
    }
    // Create a memoized function to view attachments
    const viewAttachment = React.useCallback((rowData) => {
        console.log(rowData)
        if (rowData?.attachment3?.length) {
            setAttachment(rowData);
            setAttDialog(true);
        }
    }, []);
    const actionAttTemplate = (rowData) => {
        if (rowData.assessmentEndDate) {
            return 'NA'
        }

        let count = rowData?.attachment3?.length || 0
        return (
            <div>
                <label htmlFor={rowData.id + 'three'} className='cur-pointer clr-navy text-underline fw-7' >Add <input type='file' accept=".pdf,.xls,.xlsx,.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
                    id={rowData.id + 'three'} hidden onChange={(e) => { handleReportAttachmentUpload(e, rowData.id, 'attachment3') }} ></input> </label>
                {count > 0 && <label className='cur-pointer clr-navy fw-7' onClick={() => { viewAttachment(rowData) }} > / View</label>}
            </div>
        )



    }
    const auditAttTemplate = (rowData) => {

        return !rowData.assessmentEndDate ? rowData?.attachment2?.length ? <span className='mandatory cur-pointer text-underline' onClick={() => { handleRemoveAttachment(rowData.id, 'attachment2') }}>Remove</span> : <label htmlFor={rowData.id + 'two'} className='cur-pointer clr-navy text-underline fw-7' >Add <input type='file' accept=".pdf,.xls,.xlsx,.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            id={rowData.id + 'two'} hidden onChange={(e) => { handleReportAttachmentUpload(e, rowData.id, 'attachment2') }} ></input> </label> : 'NA'

    }
     console.log("FilterDatata",globalFilter);
     console.log("datasdatas",datas);
    return (
        <div>
            <div className="col-12 flex justify-content-between align-items-end mb-3 gap-4">
                {/* Date Range Filter - Left */}
                <div className="flex gap-3 align-items-center">
                    <div className="flex flex-column">
                        <label className="mb-1">Date Range From</label>
                        <Calendar
                            value={dateFilter.start}
                            onChange={(e) => setDateFilter({ ...dateFilter, start: e.value })}
                            placeholder="Start Date"
                            dateFormat="dd-mm-yy"
                            showIcon
                        />
                    </div>
                    <div className="flex flex-column">
                        <label className="mb-1">To</label>
                        <Calendar
                            value={dateFilter.end}
                            onChange={(e) => setDateFilter({ ...dateFilter, end: e.value })}
                            placeholder="End Date"
                            dateFormat="dd-mm-yy"
                            showIcon
                            minDate={dateFilter.start}
                            disabled={!dateFilter.start}
                        />
                    </div>
                    {(dateFilter.start || dateFilter.end) && (
                        <button
                            className="btn btn-sm btn-outline-secondary align-self-end mb-1"
                            onClick={clearDateFilter}
                            style={{ height: '36px' }}
                        >
                            Clear
                        </button>
                    )}
                </div>

                {/* Search Bar - Right */}
                <div style={{ minWidth: '300px' }}>
                    <span className="p-input-icon-left" style={{ width: '100%' }}>
                        <i className="pi pi-search" />
                        <InputText
                            value={search}
                            style={{ width: '100%' }}
                            onChange={searchFn}
                            placeholder="Search Code/Name"
                        />
                    </span>
                </div>
            </div>
            <div className="d-flex justify-content-between align-items-center mb-3">
    <h4>Suppliers Completed ({datas.length})</h4>
    <button
        className="btn btn-sm btn-success"
        onClick={exportExcel}
    >
        Download Excel
    </button>
</div>

            <DataTable
                value={datas}
                paginator
                rows={10}
                scrollable
                scrollHeight="500px"
                filters={tableFilters}
                filterDisplay="menu"
                onFilter={(e) => {
                    // Create a copy of the filters object
                    const cleanedFilters = { ...e.filters };

                    // Remove the null key if it exists
                    if (cleanedFilters.hasOwnProperty('null')) {
                        delete cleanedFilters['null'];
                    }

                    // Update our tableFilters state
                    setTableFilters(cleanedFilters);
                    console.log('Filter applied:', cleanedFilters);
                }}
                className="mt-2 h-500">
                <Column sortable field="tableIndex" header="S.No" body={(rowData, options) => rowData.tableIndex || options.rowIndex + 1} sortFunction={(e) => {
                    const { data, order } = e;
                    const indexedData = data.map((item, index) => ({ ...item, tableIndex: index + 1 }));
                    return order === 1 ?
                        indexedData.sort((a, b) => a.tableIndex - b.tableIndex) :
                        indexedData.sort((a, b) => b.tableIndex - a.tableIndex);
                }} />
                <Column field="msiId" header="Calibration ID" body={calibrationIdBodyTemplate} showFilterMatchModes={false}
                    filter
                    sortable
                    sortFunction={sortCalibrationId}
                    filterElement={(options) =>
                        RowFilterTemplate(options, "msiId")
                    }
                    ></Column>
                <Column field="supplierName" header="Name" body={nameTemplate} showFilterMatchModes={false}
                    filter
                    sortable
                    sortFunction={sortName}
                    filterElement={(options) =>
                        RowFilterTemplate(options, "supplierName")
                    } ></Column>
                <Column field="location" header="Location" body={locationTemplate} showFilterMatchModes={false}
                    filter
                    sortable
                    sortFunction={sortLocation}
                    filterElement={(options) =>
                        RowFilterTemplate(options, "location")
                    }></Column>
                <Column field="supplierContact" header="Supplier Contact" body={contactTemplate}></Column>

                <Column field="cat" header="Category" body={categoryTemplate} sortable sortFunction={sortCategory}
                    filter
                    showFilterMatchModes={false}
                    filterElement={(options) =>
                        RowFilterTemplate(options, "cat")
                    }
                ></Column>
                <Column field="selfAssessmentDueDate" header="Self-Assessment Submission Date" body={assessmentDueDate} sortable sortFunction={sortSelfAssessmentDate}></Column>

                <Column field="supplierAssignmentSubmission.supplierMSIScore" header="MSI Self-assessment Score" body={supplierScoreTemplate} sortable sortFunction={sortSelfAssessmentScore}></Column>
                <Column field="calibrationStartDate" sortable sortFunction={sortAuditStartDate} header="Calibration Start Date" body={calibrationStartDate}></Column>
                <Column field="calibrationEndDate" sortable sortFunction={sortAuditEndDate} header="Calibration End Date" body={calibrationEndDate}></Column>
                {/* <Column field="calibrationStatus" header="Calibration Status" ></Column> */}
                {/* <Column field="report" header="Report" ></Column> */}
                <Column field="auditorAssignmentSubmission.auditorMSIScore" header="MSI Score" body={auditorScoreTemplate} sortable sortFunction={sortMSIScore}></Column>
                <Column
                    field="stat"
                    header="MSI Rating"
                    body={ratingTemplate}
                    sortable
                    sortField="auditorAssignmentSubmission.auditorMSIScore"
                    filter
                    showFilterMatchModes={false}
                    filterElement={(options) =>
                        RowFilterTemplate2(options, "stat")
                    }
                />
                <Column  header="Send Report" body={sendReportTemplate}></Column>


                <Column field="supplierActions" header="No. of Actions Assigned" body={actionsAssignedTemplate}></Column>
                <Column field="supplierActions" header="Action Plan Status" body={actionPlanStatusTemplate}></Column>
                <Column field="supplierActions" header="Actions Awaiting Supplier Response" body={actionsAwaitingSupplierResponseTemplate}></Column>
                <Column field="supplierActions" header="Actions to be Reviewed by TVS" body={actionsToBeReviewedTemplate}></Column>
                <Column field="supplierActions" header="Closed Actions" body={closedActionsTemplate}></Column>

                <Column header="Calibiration Attachment" body={caliAttTemplate}></Column>
                <Column header="Action Plan Status Report" body={actionAttTemplate}></Column>
                <Column header="Action Plan Attachment" body={auditAttTemplate}></Column>
                <Column header="Add Action" body={addActionTemplate}></Column>
                <Column
                    field="team1Members"
                    header="Calibration Team Member 1"
                    body={team1Template}
                    filter
                    showFilterMatchModes={false}
                    filterElement={(options) => {
                        // Create options from team1Members in all rows
                        const teamOptions = datas
                            .filter(item => item.team1Members && item.team1Members.length > 0)
                            .flatMap(item => item.team1Members);

                        // Remove duplicates
                        const uniqueOptions = [...new Set(teamOptions)].map(name => ({
                            label: name,
                            value: name
                        }));

                        return (
                            <MultiSelect
                                value={options.value}
                                options={uniqueOptions}
                                optionLabel="label"
                                onChange={(e) => options.filterCallback(e.value)}
                                placeholder="Select Member"
                                filter
                                panelClassName='hidefilter'
                                className="p-column-filter"
                                maxSelectedLabels={1}
                                style={{ minWidth: "14rem" }}
                            />
                        );
                    }}
                    filterFunction={(value, filter) => {
                        if (!filter || filter.length === 0) return true;

                        // Use the team1Members array we stored in the rowData
                        const teamMembers = value.team1Members || [];

                        // If there are no team members and the row shows "Not Assigned", don't match any filter
                        if (teamMembers.length === 0) return false;

                        // Check if any of the selected filter values match any team member
                        return filter.some(filterValue => {
                            return teamMembers.some(member => member.includes(filterValue) || filterValue.includes(member));
                        });
                    }}
                ></Column>
                <Column
                    field="team2Members"
                    header="Calibration Team Member 2"
                    body={team2Template}
                    filter
                    showFilterMatchModes={false}
                    filterElement={(options) => {
                        // Create options from team2Members in all rows
                        const teamOptions = datas
                            .filter(item => item.team2Members && item.team2Members.length > 0)
                            .flatMap(item => item.team2Members);

                        // Remove duplicates
                        const uniqueOptions = [...new Set(teamOptions)].map(name => ({
                            label: name,
                            value: name
                        }));

                        return (
                            <MultiSelect
                                value={options.value}
                                options={uniqueOptions}
                                optionLabel="label"
                                onChange={(e) => options.filterCallback(e.value)}
                                placeholder="Select Member"
                                filter
                                panelClassName='hidefilter'
                                className="p-column-filter"
                                maxSelectedLabels={1}
                                style={{ minWidth: "14rem" }}
                            />
                        );
                    }}
                    filterFunction={(value, filter) => {
                        if (!filter || filter.length === 0) return true;

                        // Use the team2Members array we stored in the rowData
                        const teamMembers = value.team2Members || [];

                        // If there are no team members and the row shows "Not Assigned", don't match any filter
                        if (teamMembers.length === 0) return false;

                        // Check if any of the selected filter values match any team member
                        return filter.some(filterValue => {
                            return teamMembers.some(member => member.includes(filterValue) || filterValue.includes(member));
                        });
                    }}
                ></Column>
                <Column
                    field="team3Members"
                    header="Calibration Team Member 3"
                    body={team3Template}
                    filter
                    showFilterMatchModes={false}
                    filterElement={(options) => {
                        // Create options from team3Members in all rows
                        const teamOptions = datas
                            .filter(item => item.team3Members && item.team3Members.length > 0)
                            .flatMap(item => item.team3Members);

                        // Remove duplicates
                        const uniqueOptions = [...new Set(teamOptions)].map(name => ({
                            label: name,
                            value: name
                        }));

                        return (
                            <MultiSelect
                                value={options.value}
                                options={uniqueOptions}
                                optionLabel="label"
                                onChange={(e) => options.filterCallback(e.value)}
                                placeholder="Select Member"
                                filter
                                panelClassName='hidefilter'
                                className="p-column-filter"
                                maxSelectedLabels={1}
                                style={{ minWidth: "14rem" }}
                            />
                        );
                    }}
                    filterFunction={(value, filter) => {
                        if (!filter || filter.length === 0) return true;

                        // Use the team3Members array we stored in the rowData
                        const teamMembers = value.team3Members || [];

                        // If there are no team members and the row shows "Not Assigned", don't match any filter
                        if (teamMembers.length === 0) return false;

                        // Check if any of the selected filter values match any team member
                        return filter.some(filterValue => {
                            return teamMembers.some(member => member.includes(filterValue) || filterValue.includes(member));
                        });
                    }}
                ></Column>
                <Column
                    field="team4Members"
                    header="Calibration Team Member 4"
                    body={team4Template}
                    filter
                    showFilterMatchModes={false}
                    filterElement={(options) => {
                        // Create options from team4Members in all rows
                        const teamOptions = datas
                            .filter(item => item.team4Members && item.team4Members.length > 0)
                            .flatMap(item => item.team4Members);

                        // Remove duplicates
                        const uniqueOptions = [...new Set(teamOptions)].map(name => ({
                            label: name,
                            value: name
                        }));

                        return (
                            <MultiSelect
                                value={options.value}
                                options={uniqueOptions}
                                optionLabel="label"
                                onChange={(e) => options.filterCallback(e.value)}
                                placeholder="Select Member"
                                filter
                                panelClassName='hidefilter'
                                className="p-column-filter"
                                maxSelectedLabels={1}
                                style={{ minWidth: "14rem" }}
                            />
                        );
                    }}
                    filterFunction={(value, filter) => {
                        if (!filter || filter.length === 0) return true;

                        // Use the team4Members array we stored in the rowData
                        const teamMembers = value.team4Members || [];

                        // If there are no team members and the row shows "Not Assigned", don't match any filter
                        if (teamMembers.length === 0) return false;

                        // Check if any of the selected filter values match any team member
                        return filter.some(filterValue => {
                            return teamMembers.some(member => member.includes(filterValue) || filterValue.includes(member));
                        });
                    }}
                ></Column>
                {/* <Column header="Schedule MSI Calibration" body={actionBodytemplate}></Column> */}

            </DataTable>
            {selectedAudit && <Dialog
                visible={actionModal}
                header={`TVS Motors Supplier MSI Self-assessment ( ${getCalibirationId(selectedAudit)} )`}
                style={{ width: '75%' }}
                onHide={() => setActionModal(false)}


            >



                <SupplierPanel vendorCode={selectedAudit.vendorCode} closeModal={(e) => { setActionModal(() => e); forceUpdate(); }} readOnly={true} auditId={selectedAudit} />



            </Dialog>
            }

            <Dialog
                visible={actionModal2}
                style={{ width: '75%' }}
                onHide={() => { setActionModal2(false); setTimeout(() => { setSelectedAudit(null) }, 500) }}


            >

                <AuditPanel auditId={selectedAudit} editable={false} closeModal={(e) => { setActionModal2(e) }} />



            </Dialog>
            <Dialog visible={reportdialog} style={{ width: '90%' }} className="custom-dialog" onHide={() => { setReportDialog(false) }} >
                <SupplierReport report={selectedAudit} />
            </Dialog>
            <Dialog visible={docdialog} style={{ width: '90%', height: '90vh' }} className="custom-dialog" onHide={() => { setDocDialog(false) }} >
                <DocViewer style={{ height: 500 }} documents={[{ uri: API.Docs + selectedAttachments[0]?.originalname }]} pluginRenderers={DocViewerRenderers} />
            </Dialog>

            <Dialog visible={actionStatusReport} style={{ width: '90%' }} className="custom-dialog" onHide={() => { setActionStatusReport(false) }} >
                <ActionOfStatus report={actionReportData} />
            </Dialog>

            {/* Add Action Dialog */}
            <AddActionDialog
                visible={addActionDialogVisible}
                onHide={() => setAddActionDialogVisible(false)}
                supplier={selectedSupplier}
                onActionAdded={handleActionAdded}
            />
            <Dialog
                visible={attdialog}
                style={{
                    width: "50%",
                }}
                header="Attachments"
                modal
                className="p-fluid"
                onHide={() => {
                    setAttDialog(false);
                }}
            >
                <div>
                    {attachment?.attachment3?.map((i, index) => {
                        return (
                            <>
                                <div>
                                    <div
                                        style={{
                                            display: "flex",
                                            flexDirection: "row",
                                            marginTop: 20,
                                        }}
                                    >
                                        <label style={{ width: "80%" }}>{i.originalname}</label>
                                        <span
                                            style={{
                                                display: "flex",
                                                width: "20%",
                                                flexDirection: "row",
                                                justifyContent: "center",
                                                color: "green",
                                                cursor: "pointer",
                                                alignItems: "center",
                                            }}
                                            onClick={() => {
                                                window.open(API.Docs + i.originalname);
                                            }}
                                        >
                                            <i className='pi pi-trash mandatory fs-14 mr-2' onClick={(e) => { e.stopPropagation(); handleRemoveAttachment(attachment.id, 'attachment3', index) }}></i>
                                            <i className="material-icons"> visibility </i>
                                        </span>
                                    </div>
                                </div>
                            </>
                        );
                    })}
                </div>
            </Dialog>
        </div>

    );
};

export default SuppliersTableCompleted;
